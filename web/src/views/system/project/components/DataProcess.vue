<template>
	<div>
		<div class="mt-8">
			<!-- 进度条 - 文件上传到后端 -->
			<div class="text-sm text-gray-500 mb-2">Status: {{ isDoneFileUpload ? 'File Upload Completed' : (isUploadingFile ? 'Uploading Files' : 'Not Started') }}</div>
			<div v-if="isUploadingFile || isDoneFileUpload" class="bg-white rounded-xl p-8 shadow-lg border border-gray-100 mb-6">
				<div class="text-center mb-6">
					<el-icon class="text-purple-500 text-4xl mb-3" v-if="!isDoneFileUpload"><Loading /></el-icon>
					<el-icon class="text-green-500 text-4xl mb-3" v-else><Check /></el-icon>
					<h3 class="text-lg font-medium">File Upload to Backend</h3>
					<p class="text-gray-500 mt-2">Uploading files to server, please wait...</p>
				</div>
				<el-progress
					:percentage="percentProgressFileUpload"
					:stroke-width="16"
					class="mb-6"
					:status="percentProgressFileUpload === 100 ? 'success' : ''"
				/>
				<div class="grid grid-cols-4 gap-4">
					<div
						v-for="(stat, index) in analyzeStatsFileUpload"
						:key="index"
						class="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-lg p-4"
					>
						<h4 class="text-gray-600 text-sm mb-2">{{ stat.label }}</h4>
						<p class="text-2xl font-semibold text-purple-600">{{ stat.value }}</p>
					</div>
				</div>
			</div>

			<!-- 进度条 - minio文件保存 -->
			<div class="text-sm text-gray-500 mb-2">Status: {{ isDoneMinio ? 'Saved Minio' : (isSavingMinio ? 'SavingMinIo' : 'Not saved MinIo') }}</div>
			<div v-if="isSavingMinio" class="bg-white rounded-xl p-8 shadow-lg border border-gray-100">
				<div class="text-center mb-6">
					<el-icon class="text-blue-500 text-4xl mb-3" v-if="!isDoneMinio"><Loading /></el-icon>
					<h3 class="text-lg font-medium">Files save to MinIo</h3>
					<p class="text-gray-500 mt-2">Processing file upload & save, please wait...</p>
				</div>
				<el-progress :percentage="percentProgressMinio" :stroke-width="16" class="mb-6" />
				<div class="grid grid-cols-4 gap-4">
					<div v-for="(stat, index) in analyzeStatsMinio" :key="index" class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-4">
						<h4 class="text-gray-600 text-sm mb-2">{{ stat.label }}</h4>
						<p class="text-2xl font-semibold text-blue-600">{{ stat.value }}</p>
					</div>
				</div>
			</div>
		</div>
		<div class="mt-8">
			<!-- 新增的解析进度条 -->
			<div class="text-sm text-gray-500 mb-2">Status: {{ isDoneParseSaveInfluxdb ? 'Saved InfluxDB' : (isParsingSaveInfluxdb ? 'Saving InfluxDB' : 'Not saved InfluxDB') }}</div>
			<div v-if="isParsingSaveInfluxdb" class="bg-white rounded-xl p-8 shadow-lg border border-gray-100">
				<div class="text-center mb-6">
					<el-icon class="text-green-500 text-4xl mb-3" v-if="!isDoneParseSaveInfluxdb"><Loading /></el-icon>
					<span v-if="!isDoneParseSaveInfluxdb">Parsing Now</span>
					<h3 class="text-lg font-medium">Data parse & Save to InfluxDB</h3>
					<p class="text-gray-500 mt-2">Parsing & Saving multiple files, please wait...</p>
				</div>
				<el-progress 
					:percentage="percentProgressInfluxdb" 
					:stroke-width="16" 
					class="mb-6"
					:status="percentProgressInfluxdb === 100 ? 'success' : ''"
				/>
				<div class="grid grid-cols-4 gap-4">
					<div 
						v-for="(stat, index) in analyzeStatsInfluxdb" 
						:key="index" 
						class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-4"
					>
						<h4 class="text-gray-600 text-sm mb-2">{{ stat.label }}</h4>
						<p class="text-2xl font-semibold text-green-600">{{ stat.value }}</p>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, onBeforeUnmount, toRaw } from 'vue';
import { Loading, Check } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { Local, Session } from '/@/utils/storage';
import { getBaseURL } from '/@/utils/baseUrl';
import { dataPipeline } from '/@/stores/dataPipeline';
import { v4 as uuidv4 } from 'uuid';

const baseBackendUrl = getBaseURL()
const store = dataPipeline();
const emit = defineEmits(['processComplete']);

const props = defineProps<{
  disabled?: boolean;
  currentStepIndex: number;
  uploadFormData?: FormData | null;
}>();

const isSavingMinio = ref(false);
const percentProgressMinio = ref(0);
const analyzeStatsMinio = ref([
	{ label: 'Files Processed', value: '0' },
	{ label: 'Total Files', value: '0' },
	{ label: 'Progress', value: '0%' },
	{ label: 'Status', value: 'Processing' }
]);

// 文件上传相关状态
const isDoneFileUpload = ref(false);
const isUploadingFile = ref(false);
const percentProgressFileUpload = ref(0);
const analyzeStatsFileUpload = ref([
	{ label: 'Files Uploaded', value: '0' },
	{ label: 'Total Files', value: '0' },
	{ label: 'Progress', value: '0%' },
	{ label: 'Status', value: 'Not Started' }
]);

// 表单数据
const form = ref({
	projectNumber: 'EP2002'  // 项目编号
});

// 随机生成一个5个字符的tag
function generateRandom5CharString() {
	// 生成 UUID（如: '9b1deb4d-3b7d-4bad-9bdd-2b0d7b3dcb6d'）
    const uuid = uuidv4().replace(/-/g, ''); // 去掉连字符，变成 32 位 hex 字符串
    return uuid.substring(0, 5); // 取前 5 个字符作为 tag
}
const random5CharIDString = generateRandom5CharString();

const isDoneMinio = ref(false)
// 修复TypeScript类型错误
let eventSource2: EventSource | null = null;
let binFiles_Minio_info: Array<{object_name: string, version_id: string}> = []

// 文件上传SSE相关变量
let fileUploadEventSource: EventSource | null = null;
let file_dataBase_info: any[] = [];
let isFileUploadProcessing = false; // 防止重复调用

// 文件上传SSE处理函数
const fileUploadProcess = async () => {
  // 防止重复调用
  if (isFileUploadProcessing) {
    console.log('文件上传正在进行中，跳过重复调用');
    return;
  }

  isFileUploadProcessing = true;

  // 如果存在之前的连接，先关闭
  if (fileUploadEventSource) {
    fileUploadEventSource.close();
  }

  // 获取formData，优先使用props中的，否则从store中获取
  const formData = props.uploadFormData || store.$state.uploadFormData;
  if (!formData) {
    console.error('没有找到上传文件数据');
    isFileUploadProcessing = false;
    return;
  }

  isUploadingFile.value = true;

  try {
    // 将 formData 转换为参数对象
    const files = formData.getAll('files') as File[];
    const isFolder = formData.get('isFolder') === 'true';
    const folderName = formData.get('folderName') || '';
    const projectType = formData.get('projectType') || '';

    // 构建 SSE 参数，将文件信息传递给后端
    const param = {
      total_files: files.length,
      is_folder: isFolder,
      folder_name: folderName,
      project_type: projectType,
      // 将文件信息序列化（只传递基本信息，文件内容通过其他方式传递）
      files_info: files.map(file => ({
        name: file.name,
        size: file.size,
        type: file.type
      }))
    };

    // 直接创建 EventSource 连接，后端将在连接建立时开始文件上传
    fileUploadEventSource = new EventSource(`${baseBackendUrl}api/data_pipeline/file/sse/upload/?param=${encodeURIComponent(JSON.stringify(param))}`);
    console.log('SSE建立连接 - 文件上传进度监控');

    fileUploadEventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('文件上传进度:', data);

        // 更新进度信息
        if (data.progress !== undefined) {
          percentProgressFileUpload.value = data.progress;
        }

        if (data.uploaded_files !== undefined && data.total_files !== undefined) {
          analyzeStatsFileUpload.value = [
            { label: 'Files Uploaded', value: data.uploaded_files.toString() },
            { label: 'Total Files', value: data.total_files.toString() },
            { label: 'Progress', value: `${data.progress}%` },
            { label: 'Status', value: data.status === 'completed' ? 'Complete' : 'Uploading' }
          ];
        }

        // 如果上传完成
        if (data.status === 'completed') {
          // 保存文件数据库信息
          file_dataBase_info = data.files || [];
          store.$state.filesPostgresqlInfo = file_dataBase_info;

          isDoneFileUpload.value = true;
          isUploadingFile.value = false;

          // 保存最终状态到 pinia
          store.$state.fileUploadInfo = {
            isDoneFileUpload: isDoneFileUpload.value,
            isUploadingFile: isUploadingFile.value,
            percentProgressFileUpload: percentProgressFileUpload.value,
            analyzeStatsFileUpload: analyzeStatsFileUpload.value,
            uploadingFiles: store.$state.fileUploadInfo.uploadingFiles
          };

          console.log('文件上传完成');
          if (fileUploadEventSource) {
            fileUploadEventSource.close();
          }
        }

        // 如果出现错误
        if (data.status === 'error') {
          throw new Error(data.message || '文件上传过程中出现错误');
        }

      } catch (parseError) {
        console.error('解析 SSE 数据失败:', parseError);
      }
    };

    fileUploadEventSource.onerror = (error) => {
      console.error('SSE 连接错误:', error);
      isUploadingFile.value = false;
      if (fileUploadEventSource) {
        fileUploadEventSource.close();
      }
    };

  } catch (error) {
    console.error('文件上传失败:', error);
    const errorMessage = error instanceof Error ? error.message : '文件上传失败';
    ElMessage.error('文件上传失败: ' + errorMessage);

    // 重置状态
    isUploadingFile.value = false;
    percentProgressFileUpload.value = 0;
    analyzeStatsFileUpload.value = [
      { label: 'Files Uploaded', value: '0' },
      { label: 'Total Files', value: '0' },
      { label: 'Progress', value: '0%' },
      { label: 'Status', value: 'Failed' }
    ];
  } finally {
    // 重置处理标志
    isFileUploadProcessing = false;
  }
}

const minioProcess = async() => {
	// 如果存在之前的连接，先关闭
	if (eventSource2) {
		eventSource2.close();
	}

	isSavingMinio.value = true;

	const param = {
		"project_name": projectName.value,
		"project_type": projectType.value,
	}

	// 创建新的 EventSource 连接
	eventSource2 = new EventSource(`${baseBackendUrl}api/data_pipeline/file/sse/minio/?param=${encodeURIComponent(JSON.stringify(param))}`);
	console.log('SSE建立连接 - minio eventSource2')
	
	eventSource2.onmessage = (event) => {
		try {
			const data = JSON.parse(event.data);
			console.log('data===========', data)
			
			if (data.status === 'Completed') {
				console.log('Minio 上传完成');
				console.log('data===========', data)

				// 先更新进度条数据到100%
				percentProgressMinio.value = data.progress;
				analyzeStatsMinio.value = [
					{ label: 'Files Processed', value: data.current.toString() },
					{ label: 'Total Files', value: data.total.toString() },
					{ label: 'Progress', value: `${data.progress}%` },
					{ label: 'Status', value: 'Complete' }
				];

				binFiles_Minio_info = data.binFiles_Minio_info
				store.$state.binFiles_Minio_info = binFiles_Minio_info

				console.log('store.$state.binFiles_Minio_info ==== ', store.$state.binFiles_Minio_info)

				// 保存最终状态到 pinia
				isDoneMinio.value = true;
				store.$state.minioInfo = {
					isDoneMinio: isDoneMinio.value,
					isSavingMinio: isSavingMinio.value,
					percentProgressMinio: percentProgressMinio.value,
					analyzeStatsMinio: analyzeStatsMinio.value
				};

				if (eventSource2) {
					eventSource2.close();
					eventSource2 = null;
				}
				return;
			}
			
			// 更新进度条
			percentProgressMinio.value = data.progress;
			
			// 更新统计信息，并保存到 pinia 里
			analyzeStatsMinio.value = [
				{ label: 'Files Processed', value: data.current.toString() },
				{ label: 'Total Files', value: data.total.toString() },
				{ label: 'Progress', value: `${data.progress}%` },
				{ label: 'Status', value: data.progress === 100 ? 'Complete' : 'Processing' }
			];

			// 如果进度达到100%，关闭连接
			if (data.progress === 100) {
				setTimeout(() => {
					// isSavingMinio.value = false;
					if (eventSource2) {
						eventSource2.close();
						eventSource2 = null;
						console.log('SSE 关闭连接 minio - eventSource2')
					}
				}, 1000); // 延迟1秒关闭，让用户看到100%的状态
				isDoneMinio.value = true
			}

			// 保存状态到 pinia
			store.$state.minioInfo = {
				isDoneMinio: isDoneMinio.value,
				isSavingMinio: isSavingMinio.value,
				percentProgressMinio: percentProgressMinio.value,
				analyzeStatsMinio: analyzeStatsMinio.value
			};
			
		} catch (error) {
			console.error('处理进度数据错误：', error);
		}
	}

	// 添加关闭事件处理
	eventSource2.addEventListener('close', () => {
		console.log('SSE连接正常关闭');
		if (eventSource2) {
			eventSource2.close();
			eventSource2 = null;
		}
	});

	eventSource2.onerror = (error) => {
		console.error('SSE连接错误: ', error);
		// 只有在非正常关闭时才设置状态为false
		if (error.target && (error.target as EventSource).readyState === EventSource.CLOSED) {
			isSavingMinio.value = false;
		}
		// 清理连接
		if (eventSource2) {
			eventSource2.close();
			eventSource2 = null;
		}
	}

	// 把相关数据保存到 pinia

}


// 添加新的响应式变量
const percentProgressInfluxdb = ref(0)
const isParsingSaveInfluxdb = ref(false)
const analyzeStatsInfluxdb = ref([
	{ label: 'Files Parsed', value: '0' },
	{ label: 'Total Files', value: '0' },
	{ label: 'Progress', value: '0%' },
	{ label: 'Status', value: 'Not Started' }
])

let parseEventSource: EventSource | null = null;
const isDoneParseSaveInfluxdb = ref(false)


const influxdbProcess = async () => {
	console.log('开始 influxdbProcess() ');
	// 获取pinia里 bin文件的数据库信息
	const binFilesPostgresqlInfo = toRaw(store.$state.filesPostgresqlInfo)
	
	binFiles_Minio_info = toRaw(store.$state.binFiles_Minio_info)
	console.log('store.$state.binFiles_Minio_info ==== ', store.$state.binFiles_Minio_info)


	// 关闭之前的连接（如果存在）
	if (parseEventSource) {
		parseEventSource.close();
	}

	isParsingSaveInfluxdb.value = true;

	const param = {
		project_name: projectName.value,
		project_type: projectType.value,
		binFilesPostgresqlInfo: binFilesPostgresqlInfo,
		binFiles_Minio_info: binFiles_Minio_info
	};

	console.log('influx SSE 请求 param ==== ', param);

	// 建立新的 SSE 连接
	parseEventSource = new EventSource(
		`${baseBackendUrl}api/data_pipeline/file/sse/influxdb/?param=${encodeURIComponent(JSON.stringify(param))}`
	);
	console.log('SSE建立连接 - 解析influxdb -parseEventSource');

	// 设置状态为 Processing
	const statusObj = analyzeStatsInfluxdb.value.find(item => item.label === 'Status');
	if (statusObj) {
		statusObj.value = 'Processing';
	}

	// 处理消息事件
	parseEventSource.onmessage = (event) => {
		try {
			const data = JSON.parse(event.data);

			// 更新进度条显示
			percentProgressInfluxdb.value = parseInt(data.progress) || 0;

			// 更新分析状态
			analyzeStatsInfluxdb.value = [
				{ label: 'Files Parsed', value: data.current },
				{ label: 'Total Files', value: data.total },
				{ label: 'Progress', value: `${data.progress}%` },
				{ label: 'Status', value: data.status || 'Processing' }
			];

			// 如果任务已完成, 把进度条数据保存到pinia, 并把 .bin文件的 开始时间/结束时间/id等所有后续用到的属性都保存pinia, 传给step2-Visualization
			if (data.status === 'Completed') {
				console.log('收到完成消息，准备关闭连接');
				if (parseEventSource) {
					parseEventSource.close();
					parseEventSource = null;
					console.log('SSE连接已关闭');
				}

				isDoneParseSaveInfluxdb.value = true;

				// 更新 store 数据
				store.$state.influxdbInfo = {
					isDoneParseSaveInfluxdb: isDoneParseSaveInfluxdb.value,
					isParsingSaveInfluxdb: isParsingSaveInfluxdb.value,
					percentProgressInfluxdb: percentProgressInfluxdb.value,
					analyzeStatsInfluxdb: analyzeStatsInfluxdb.value,
					binFile_startTime: data.binFile_startTime,
					binFile_endTime: data.binFile_endTime,
					binfile_Postgresql_id: data.binfile_Postgresql_id,
					binfile_Minio_version_id: data.binfile_Minio_version_id,
					influx_ubique_tagId: data.influx_ubique_tagId,
				};

				return;
			}

			// 如果进度没到100%, 只更新进度条
			store.$state.influxdbInfo = {
				isDoneParseSaveInfluxdb: isDoneParseSaveInfluxdb.value,
				isParsingSaveInfluxdb: isParsingSaveInfluxdb.value,
				percentProgressInfluxdb: percentProgressInfluxdb.value,
				analyzeStatsInfluxdb: analyzeStatsInfluxdb.value,
				binFile_startTime: '',
				binFile_endTime: '',
				binfile_Postgresql_id: '',
				binfile_Minio_version_id: '',
				influx_ubique_tagId: '',
			};

		} catch (error) {
			console.error('处理解析进度数据错误：', error);
		}
	};

	// 错误处理
	parseEventSource.onerror = (error) => {
		console.error('解析进度SSE连接错误: ', error);
		if (parseEventSource) {
			parseEventSource.close();
			parseEventSource = null;
		}
	};
};

// 监听文件上传进度
const watchFileUploadProgress = () => {
	const checkFileUploadComplete = setInterval(() => {
		// 从 pinia 获取最新状态
		const fileUploadInfo = store.$state.fileUploadInfo;

		// 更新本地状态
		isDoneFileUpload.value = fileUploadInfo.isDoneFileUpload;
		isUploadingFile.value = fileUploadInfo.isUploadingFile;
		percentProgressFileUpload.value = fileUploadInfo.percentProgressFileUpload;
		analyzeStatsFileUpload.value = fileUploadInfo.analyzeStatsFileUpload;

		// 如果文件上传完成，开始后续处理
		if (fileUploadInfo.isDoneFileUpload) {
			clearInterval(checkFileUploadComplete);
			console.log('文件上传完成，开始 Minio 和 InfluxDB 处理');
			startAnalysis();
		}
	}, 500);
};

const checkStatus = () => {
	const isDoneFileUploadVal = isDoneFileUpload.value;
	const isDoneMinioVal = isDoneMinio.value;
	const isDoneParseSaveInfluxdbVal = isDoneParseSaveInfluxdb.value;

	if (isDoneFileUploadVal === true && isDoneMinioVal === true && isDoneParseSaveInfluxdbVal === true) {
		console.log('文件上传、minio 和 influxdb 都完成, emit跳转')
		emit('processComplete')
	}
}
const startAnalysis = async () => {
	try {
		// 确保文件上传已完成
		if (!isDoneFileUpload.value) {
			console.log('等待文件上传完成...');
			// 开始文件上传SSE处理
			await fileUploadProcess();
			return;
		}

		await saveDataIoMinio(); // 等待 Minio 上传真正完成
		console.log('Minio 上传完成');

		// await saveDataToInfluxdb(); // 等待 Influxdb 保存真正完成
		console.log('Influxdb 保存完成');

		checkStatus(); // 现在检查状态应该是三个都完成的状态
	} catch (error) {
		console.error('数据处理过程出错：', error);
		ElMessage.error('数据处理过程出错');
	}
};

const saveDataToInfluxdb = async () => {
	console.log('saveDataToInfluxdb() 开始')
	
	if (!projectName.value || !projectType.value) {
		ElMessage.warning('请检查项目名称或项目类型');
		return;
	}
	
	// 返回 Promise，等待 SSE 完成
	return new Promise<void>((resolve) => {
		influxdbProcess().then(() => {
			const checkInfluxComplete = setInterval(() => {
				if (isDoneParseSaveInfluxdb.value) {
					clearInterval(checkInfluxComplete);
					console.log('saveDataToInfluxdb() 真正完成');
					resolve();
				}
			}, 1000);
		});
	});
}

const saveDataIoMinio = async () => {
	console.log('saveDataIoMinio() 开始')
	
	if (!projectName.value || !projectType.value) {
		ElMessage.warning('请检查项目名称或项目类型');
		return;
	}
	
	// 返回 Promise，等待 SSE 完成
	return new Promise<void>((resolve) => {
		minioProcess().then(() => {
			const checkMinioComplete = setInterval(() => {
				if (isDoneMinio.value) {
					clearInterval(checkMinioComplete);
					console.log('saveDataIoMinio() 真正完成');
					resolve();
				}
			}, 500);
		});
	});
}

const initProgressInfo = () => {
	console.log('执行 initProgressInfo()')
	// 从 pinia 获取初始值
	const { uploadFiles, fileUploadInfo, minioInfo, influxdbInfo } = dataPipeline().$state;

	// 如果是新文件上传
	if (uploadFiles) {
		// 重置 pinia 中的 uploadFiles 为 false
		dataPipeline().$state.uploadFiles = false;

		// 初始化文件上传状态
		isDoneFileUpload.value = fileUploadInfo.isDoneFileUpload;
		isUploadingFile.value = fileUploadInfo.isUploadingFile;
		percentProgressFileUpload.value = fileUploadInfo.percentProgressFileUpload;
		analyzeStatsFileUpload.value = fileUploadInfo.analyzeStatsFileUpload;

		// 监听文件上传状态变化
		watchFileUploadProgress();

		return false;
	}

	// 不是新文件上传，更新进度条状态
	isDoneFileUpload.value = fileUploadInfo.isDoneFileUpload;
	isUploadingFile.value = fileUploadInfo.isUploadingFile;
	percentProgressFileUpload.value = fileUploadInfo.percentProgressFileUpload;
	analyzeStatsFileUpload.value = fileUploadInfo.analyzeStatsFileUpload;

	isDoneMinio.value = minioInfo.isDoneMinio;
	isSavingMinio.value = minioInfo.isSavingMinio;
	percentProgressMinio.value = minioInfo.percentProgressMinio;
	analyzeStatsMinio.value = minioInfo.analyzeStatsMinio;

	// 更新 influxdb 相关状态
	isDoneParseSaveInfluxdb.value = influxdbInfo.isDoneParseSaveInfluxdb;
	isParsingSaveInfluxdb.value = influxdbInfo.isParsingSaveInfluxdb;
	percentProgressInfluxdb.value = influxdbInfo.percentProgressInfluxdb;
	analyzeStatsInfluxdb.value = influxdbInfo.analyzeStatsInfluxdb;
	// 返回是否全部完成的状态
	return isDoneFileUpload.value && isDoneMinio.value && isDoneParseSaveInfluxdb.value;
}


//获取项目基本信息,用于api请求参数
const projectType = ref('')
const projectName = ref('')
const getProInfo = () => {
	projectType.value = store.$state.projectInfo.project_type
	projectName.value = store.$state.projectInfo.project_name
}

onMounted(() => {
	getProInfo()
	const isAllDone = initProgressInfo()
	console.log('!isAllDone ============== ', !isAllDone)
	// 如果 isAllDone 为 true，说明已全部完成, 不需要重新解析；否则, 重新解析
	if (!isAllDone) {
		console.log('onMounted() 检查是否需要开始解析')
		console.log('isDoneFileUpload.value 检查文件是否已上传到后端 ============== ', isDoneFileUpload.value)
		
		// 开始分析流程
		startAnalysis()
	}
});

onBeforeUnmount(() => {
	console.log('组件注销')
	// 关闭文件上传 SSE 连接
	if (fileUploadEventSource) {
		fileUploadEventSource.close();
		fileUploadEventSource = null;
		console.log('SSE连接已关闭 - file upload fileUploadEventSource')
	}
	// 关闭 minio SSE 连接
	if (eventSource2) {
		eventSource2.close();
		eventSource2 = null;
		console.log('SSE连接已关闭 - minio eventSource2')
	}
	// 关闭 influxdb SSE 连接
	if (parseEventSource) {
		parseEventSource.close();
		parseEventSource = null;
		console.log('SSE连接已关闭 - influxdb parseEventSource')
	}
})

</script> 

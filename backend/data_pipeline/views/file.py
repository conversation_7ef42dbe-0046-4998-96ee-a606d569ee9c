from dvadmin.utils.viewset import CustomModelViewSet
from dvadmin.utils.serializers import CustomModelSerializer
from dvadmin.utils.json_response import ErrorResponse, SuccessResponse
from data_pipeline.models import DataPipelineFile
from dvadmin.utils.filters import CoreModelFilterBankend
from rest_framework.permissions import IsAuthenticated
import os
from rest_framework.decorators import action
from django.conf import settings
from datetime import datetime
import shutil

from rest_framework import status
from rest_framework.response import Response
from django.utils.dateparse import parse_datetime
from datetime import datetime
import json
import time
from django.http import StreamingHttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import threading
import glob

# ================================================= #
# ************** SSE 相关函数 ************** #
# ================================================= #

def sse_progress_view_file_upload(request):
    """
    文件上传进度 SSE 视图
    """
    param_str = request.GET.get('param', '')
    try:
        param = json.loads(param_str)
        upload_dir = param.get('upload_dir', '')
        total_files = param.get('total_files', 0)

        if not upload_dir or not total_files:
            return StreamingHttpResponse(status=400)

    except json.JSONDecodeError:
        return StreamingHttpResponse(status=400)
    except KeyError:
        return StreamingHttpResponse(status=400)

    def sse_generator():
        uploaded_count = 0
        while uploaded_count < total_files:
            try:
                # 每隔1秒检查一次上传目录中的文件数量
                time.sleep(1)

                # 统计目录中的文件数量
                if os.path.exists(upload_dir):
                    # 递归统计所有文件（包括子目录中的文件）
                    file_pattern = os.path.join(upload_dir, '**', '*')
                    all_files = glob.glob(file_pattern, recursive=True)
                    # 只统计文件，不包括目录
                    uploaded_count = len([f for f in all_files if os.path.isfile(f)])
                else:
                    uploaded_count = 0

                # 计算进度百分比
                progress = min(int((uploaded_count / total_files) * 100), 100)

                # 构造进度数据
                progress_data = {
                    'status': 'uploading',
                    'progress': progress,
                    'uploaded_files': uploaded_count,
                    'total_files': total_files,
                    'message': f'已上传 {uploaded_count}/{total_files} 个文件'
                }

                # 发送进度数据
                yield f"data: {json.dumps(progress_data)}\n\n"

                # 如果上传完成，发送完成消息并退出
                if uploaded_count >= total_files:
                    progress_data['status'] = 'completed'
                    progress_data['message'] = '文件上传完成'
                    yield f"data: {json.dumps(progress_data)}\n\n"
                    yield "event: close\ndata: close\n\n"
                    break

            except Exception as e:
                error_data = {
                    'status': 'error',
                    'message': f'监控进度时出错: {str(e)}'
                }
                yield f"data: {json.dumps(error_data)}\n\n"
                yield "event: close\ndata: close\n\n"
                break

    response = StreamingHttpResponse(
        sse_generator(),
        content_type='text/event-stream'
    )
    response['Cache-Control'] = 'no-cache'
    response['Access-Control-Allow-Origin'] = '*'
    response['Access-Control-Allow-Headers'] = 'Cache-Control'
    return response

# ================================================= #
# ************** 序列化器 ************** #
# ================================================= #

class DataPipelineFileSerializer(CustomModelSerializer):
    """
    数据管道文件-序列化器
    """
    class Meta:
        model = DataPipelineFile
        fields = "__all__"
        read_only_fields = ["id"]


# ================================================= #
# ************** 视图集 ************** #
# ================================================= #

class DataPipelineFileModelViewSet(CustomModelViewSet):
    """
    数据管道文件管理接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    upload_file:上传文件
    """
    queryset = DataPipelineFile.objects.all()
    serializer_class = DataPipelineFileSerializer
    permission_classes = [IsAuthenticated]  # 添加认证要求
    # 移除数据权限过滤器，只保留核心模型过滤器
    extra_filter_class = [CoreModelFilterBankend]
    filter_fields = ['project_number', 'file_name', 'file_status', 'version']
    search_fields = ['project_number', 'file_name']

    @action(methods=['POST'], detail=False)
    def upload_file(self, request):
        """
        文件上传处理 - 启动上传并返回监控参数
        """
        try:
            files = request.FILES.getlist('files')
            is_folder = request.POST.get('isFolder', 'false').lower() == 'true'
            folder_name = request.POST.get('folderName', '')
            project_type = request.POST.get('projectType', '')

            if not files:
                return ErrorResponse(msg='没有接收到文件')

            # 准备上传目录
            base_upload_dir = os.path.join(settings.BASE_DIR, 'data_pipeline', 'uploadFiles')

            # 如果目录存在，先清空目录
            if os.path.exists(base_upload_dir):
                try:
                    shutil.rmtree(base_upload_dir)
                except Exception as e:
                    print(f"删除文件时出错: {str(e)}")

            # 重新创建上传目录
            os.makedirs(base_upload_dir, exist_ok=True)

            # 如果是文件夹上传，创建文件夹目录
            if is_folder and folder_name:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                folder_path = f"{folder_name}_{timestamp}"
                upload_dir = os.path.join(base_upload_dir, folder_path)
                os.makedirs(upload_dir, exist_ok=True)
            else:
                upload_dir = os.path.join(base_upload_dir, 'noFolderFileList')
                os.makedirs(upload_dir, exist_ok=True)

            total_files = len(files)

            # 在后台线程中执行文件上传
            def upload_files_background():
                try:
                    uploaded_files = []
                    for file in files:
                        file_info = self._save_single_file(file, upload_dir, is_folder, folder_name)
                        if file_info:
                            uploaded_files.append(file_info)
                        # 添加小延迟以便观察进度
                        time.sleep(0.1)

                    # 将上传结果保存到全局变量或缓存中
                    # 这里可以使用 Redis 或其他缓存机制
                    setattr(self, '_uploaded_files_result', uploaded_files)

                except Exception as e:
                    print(f"后台上传文件时出错: {str(e)}")
                    setattr(self, '_upload_error', str(e))

            # 启动后台上传线程
            upload_thread = threading.Thread(target=upload_files_background)
            upload_thread.daemon = True
            upload_thread.start()

            # 返回监控参数
            return SuccessResponse(data={
                'upload_dir': upload_dir,
                'total_files': total_files,
                'message': '文件上传已启动，请通过 SSE 监控进度'
            }, msg='文件上传启动成功')

        except Exception as e:
            return ErrorResponse(msg=f'文件上传失败：{str(e)}')

    def _upload_file_with_sse(self, files, is_folder, folder_name, project_type):
        """
        使用 SSE 进行文件上传，返回进度信息
        """
        try:
            # 准备上传目录
            base_upload_dir = os.path.join(settings.BASE_DIR, 'data_pipeline', 'uploadFiles')

            # 如果目录存在，先清空目录
            if os.path.exists(base_upload_dir):
                try:
                    shutil.rmtree(base_upload_dir)
                except Exception as e:
                    print(f"删除文件时出错: {str(e)}")

            # 重新创建上传目录
            os.makedirs(base_upload_dir, exist_ok=True)

            # 如果是文件夹上传，创建文件夹目录
            if is_folder and folder_name:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                folder_path = f"{folder_name}_{timestamp}"
                upload_dir = os.path.join(base_upload_dir, folder_path)
                os.makedirs(upload_dir, exist_ok=True)
            else:
                upload_dir = os.path.join(base_upload_dir, 'noFolderFileList')
                os.makedirs(upload_dir, exist_ok=True)

            total_files = len(files)

            # 在后台线程中执行文件上传
            def upload_files_background():
                try:
                    for file in files:
                        self._save_single_file(file, upload_dir, is_folder, folder_name)
                        # 添加小延迟以便观察进度
                        time.sleep(0.1)
                except Exception as e:
                    print(f"后台上传文件时出错: {str(e)}")

            # 启动后台上传线程
            upload_thread = threading.Thread(target=upload_files_background)
            upload_thread.daemon = True
            upload_thread.start()

            # 生成 SSE 流
            def sse_generator():
                uploaded_count = 0
                while uploaded_count < total_files:
                    try:
                        # 每隔1秒检查一次上传目录中的文件数量
                        time.sleep(1)

                        # 统计目录中的文件数量
                        if os.path.exists(upload_dir):
                            # 递归统计所有文件（包括子目录中的文件）
                            file_pattern = os.path.join(upload_dir, '**', '*')
                            all_files = glob.glob(file_pattern, recursive=True)
                            # 只统计文件，不包括目录
                            uploaded_count = len([f for f in all_files if os.path.isfile(f)])
                        else:
                            uploaded_count = 0

                        # 计算进度百分比
                        progress = min(int((uploaded_count / total_files) * 100), 100)

                        # 构造进度数据
                        progress_data = {
                            'status': 'uploading',
                            'progress': progress,
                            'uploaded_files': uploaded_count,
                            'total_files': total_files,
                            'message': f'已上传 {uploaded_count}/{total_files} 个文件'
                        }

                        # 发送进度数据
                        yield f"data: {json.dumps(progress_data)}\n\n"

                        # 如果上传完成，发送完成消息并退出
                        if uploaded_count >= total_files:
                            progress_data['status'] = 'completed'
                            progress_data['message'] = '文件上传完成'
                            yield f"data: {json.dumps(progress_data)}\n\n"
                            yield "event: close\ndata: close\n\n"
                            break

                    except Exception as e:
                        error_data = {
                            'status': 'error',
                            'message': f'监控进度时出错: {str(e)}'
                        }
                        yield f"data: {json.dumps(error_data)}\n\n"
                        yield "event: close\ndata: close\n\n"
                        break

            # 返回 SSE 响应
            response = StreamingHttpResponse(
                sse_generator(),
                content_type='text/event-stream'
            )
            response['Cache-Control'] = 'no-cache'
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Headers'] = 'Cache-Control'
            return response

        except Exception as e:
            return ErrorResponse(msg=f'SSE 文件上传失败：{str(e)}')

    def _upload_file_sync(self, files, is_folder, folder_name, project_type):
        """
        同步文件上传（原有逻辑）
        """
        # 基础上传目录
        base_upload_dir = os.path.join(settings.BASE_DIR, 'data_pipeline', 'uploadFiles')

        # 如果目录存在，先清空目录
        if os.path.exists(base_upload_dir):
            try:
                shutil.rmtree(base_upload_dir)
            except Exception as e:
                print(f"删除文件时出错: {str(e)}")

        # 重新创建上传目录
        os.makedirs(base_upload_dir, exist_ok=True)

        # 如果是文件夹上传，创建文件夹目录
        if is_folder and folder_name:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            folder_path = f"{folder_name}_{timestamp}"
            upload_dir = os.path.join(base_upload_dir, folder_path)
            os.makedirs(upload_dir, exist_ok=True)
        else:
            upload_dir = os.path.join(base_upload_dir, 'noFolderFileList')

        uploaded_files = []
        for file in files:
            try:
                file_info = self._save_single_file(file, upload_dir, is_folder, folder_name)
                if file_info:
                    uploaded_files.append(file_info)
            except Exception as e:
                print(f"处理文件 {file.name} 时出错: {str(e)}")
                continue

        if not uploaded_files:
            return ErrorResponse(msg='没有文件上传成功')

        return SuccessResponse(data=uploaded_files, msg='文件上传成功')

    def _save_single_file(self, file, upload_dir, is_folder, folder_name):
        """
        保存单个文件
        """
        try:
            # 构建项目编号：如果是文件夹则使用文件夹名称，否则使用temp
            project_number = folder_name if is_folder else "temp"

            # 检查文件是否已存在
            existing_file = DataPipelineFile.objects.filter(
                project_number=project_number,
                file_name=file.name
            ).first()

            if existing_file:
                latest_version = DataPipelineFile.objects.filter(
                    project_number=project_number,
                    file_name=file.name
                ).order_by('-version').first().version
                new_version = latest_version + 1
            else:
                new_version = 1

            # 保持原始的文件路径结构（如果是文件夹上传）
            if is_folder:
                # 获取文件的相对路径
                relative_path = file.name
                if '/' in relative_path:
                    # 创建子文件夹
                    sub_folder = os.path.dirname(relative_path)
                    full_sub_folder = os.path.join(upload_dir, sub_folder)
                    os.makedirs(full_sub_folder, exist_ok=True)

                    # 文件名使用最后一部分
                    file_name = os.path.basename(relative_path)
                else:
                    file_name = relative_path
            else:
                file_name = file.name

            # 生成安全的文件名
            safe_filename = f"{file_name}"

            # 构建完整的文件路径
            if is_folder and '/' in file.name:
                sub_folder = os.path.dirname(file.name)
                file_path = os.path.join(upload_dir, sub_folder, safe_filename)
            else:
                file_path = os.path.join(upload_dir, safe_filename)

            # 确保目标目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # 写入文件
            with open(file_path, 'wb+') as destination:
                for chunk in file.chunks():
                    destination.write(chunk)

            # 创建文件记录
            file_record = DataPipelineFile.objects.create(
                project_number=project_number,
                file_name=file_name,
                file_path=file_path,
                file_size=file.size,
                file_status=1,
                version=new_version
            )

            return {
                'id': file_record.id,
                'name': file_name,
                'size': file.size,
                'path': file_path,
                'version': new_version
            }

        except Exception as e:
            print(f"保存文件 {file.name} 时出错: {str(e)}")
            return None